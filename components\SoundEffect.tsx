import React, { useRef, useEffect, useCallback } from 'react';

interface SoundEffectProps {
  audioSrc: string;
  play: boolean;
  volume?: number;
  onEnded?: () => void;
  onError?: (error: string) => void;
  loop?: boolean;
}

const SoundEffect: React.FC<SoundEffectProps> = ({
  audioSrc,
  play,
  volume = 1.0,
  onEnded,
  onError,
  loop = false
}) => {
  const audioRef = useRef<HTMLAudioElement | null>(null);

  const handleAudioEnd = useCallback(() => {
    if (onEnded) {
      onEnded();
    }
  }, [onEnded]);

  const handleAudioError = useCallback(() => {
    if (onError) {
      onError(`Failed to load audio: ${audioSrc}`);
    }
  }, [onError, audioSrc]);

  useEffect(() => {
    if (audioRef.current) {
      audioRef.current.volume = Math.max(0, Math.min(1, volume));
    }
  }, [volume]);

  useEffect(() => {
    if (play && audioRef.current) {
      // Reset audio to beginning and play
      audioRef.current.currentTime = 0;
      const playPromise = audioRef.current.play();
      
      if (playPromise !== undefined) {
        playPromise.catch((error) => {
          console.error('Error playing audio:', error);
          if (onError) {
            onError(`Failed to play audio: ${error.message}`);
          }
        });
      }
    } else if (!play && audioRef.current) {
      audioRef.current.pause();
    }
  }, [play, onError]);

  return (
    <audio
      ref={audioRef}
      src={audioSrc}
      onEnded={handleAudioEnd}
      onError={handleAudioError}
      loop={loop}
      preload="auto"
      style={{ display: 'none' }}
    />
  );
};

export default SoundEffect;
