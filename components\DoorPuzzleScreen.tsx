
import React, { useState } from 'react';
import { Puzzle } from '../types';
import { DoorIcon, KeyIcon } from './icons';
import LoadingIndicator from './LoadingIndicator';

interface DoorPuzzleScreenProps {
  puzzle: Puzzle;
  onSolve: (answer: string) => void;
  isLoading: boolean;
  attemptMessage: string | null;
}

const DoorPuzzleScreen: React.FC<DoorPuzzleScreenProps> = ({ puzzle, onSolve, isLoading, attemptMessage }) => {
  const [answer, setAnswer] = useState('');

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSolve(answer.trim());
  };

  return (
    <div className="min-h-screen flex flex-col items-center justify-center p-4 bg-gradient-to-br from-slate-800 to-slate-900 text-slate-100">
      <div className="bg-slate-700 p-8 rounded-xl shadow-2xl w-full max-w-2xl transform transition-all hover:scale-105 duration-300">
        <div className="flex items-center mb-6">
          <DoorIcon className="w-16 h-16 text-sky-400 mr-4" />
          <div>
            <h1 className="text-4xl font-bold text-sky-300">{puzzle.doorTitle}</h1>
            <p className="text-slate-300">Puzzle {puzzle.id} of 3</p>
          </div>
        </div>
        
        <p className="mb-4 text-slate-200 italic text-lg">{puzzle.question}</p>
        
        {puzzle.imagePath && (
          <div className="my-6 flex justify-center bg-slate-800 p-4 rounded-lg">
            <img 
              src={puzzle.imagePath} 
              alt={`Cipher for ${puzzle.doorTitle}`} 
              className="max-w-xs md:max-w-sm rounded shadow-lg" 
            />
          </div>
        )}

        <p className="mb-6 text-slate-300 text-sm">{puzzle.description}</p>

        <form onSubmit={handleSubmit} className="space-y-6">
          <div>
            <label htmlFor={`answer-${puzzle.id}`} className="block text-sm font-medium text-sky-300 mb-1">
              Your Answer:
            </label>
            <input
              id={`answer-${puzzle.id}`}
              type="text"
              value={answer}
              onChange={(e) => setAnswer(e.target.value)}
              className="w-full px-4 py-3 bg-slate-800 border border-slate-600 rounded-lg shadow-sm focus:ring-2 focus:ring-sky-500 focus:border-sky-500 placeholder-slate-500 text-slate-100"
              placeholder="Enter your answer here"
              disabled={isLoading}
            />
          </div>

          {attemptMessage && (
            <p className={`text-sm ${attemptMessage.startsWith('Correct') ? 'text-green-400' : 'text-red-400'}`}>
              {attemptMessage}
            </p>
          )}

          {isLoading ? (
            <LoadingIndicator />
          ) : (
            <button
              type="submit"
              className="w-full flex items-center justify-center px-6 py-3 border border-transparent text-base font-medium rounded-lg shadow-sm text-white bg-sky-600 hover:bg-sky-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-slate-800 focus:ring-sky-500 transition-colors duration-150"
              disabled={isLoading}
            >
              <KeyIcon className="w-5 h-5 mr-2" />
              Unlock Door
            </button>
          )}
        </form>
      </div>
    </div>
  );
};

export default DoorPuzzleScreen;
