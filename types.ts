
export enum GameStage {
  INTRO,
  DOOR_1_<PERSON><PERSON><PERSON><PERSON><PERSON>,
  DOOR_1_HINT_REVEAL,
  DOOR_2_PUZ<PERSON><PERSON>,
  DOOR_2_HINT_REVEAL,
  DOOR_3_PUZZLE,
  DOOR_3_HINT_REVEAL,
  CASTLE_CHALLENGE,
  VICTORY_REVEAL,
}

export interface Puzzle {
  id: number;
  doorTitle: string;
  question: string;
  description: string;
  expectedAnswer: string; // For simpler puzzles, can be a keyword or phrase
  hintAfterSolve: string;
  imagePath?: string; // Optional path for an image associated with the puzzle
}
