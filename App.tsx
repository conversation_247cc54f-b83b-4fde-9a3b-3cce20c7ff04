import React, { useState, useEffect, useCallback } from "react";
import { GameStage, Puzzle } from "./types";
import { PUZZLES_DATA, FINAL_ANSWER, HINTS_CONTENT } from "./constants";
import DoorPuzzleScreen from "./components/DoorPuzzleScreen";
import HintRevealScreen from "./components/HintRevealScreen";
import CastleChallengeScreen from "./components/CastleChallengeScreen";
import VictoryScreen from "./components/VictoryScreen";
import WorldTransition from "./components/WorldTransition";
import { generateFinalImage } from "./services/imageService";
import { KeyIcon, CastleIcon } from "./components/icons";

const normalizeAnswer = (str: string): string => {
  if (typeof str !== "string") return "";
  return str
    .toLowerCase()
    .normalize("NFD") // Decompose combined diacritical marks
    .replace(/[\u0300-\u036f]/g, "") // Remove diacritical marks (e.g., accents)
    .replace(/đ/g, "d") // Convert 'đ' to 'd'
    .trim();
};

const App: React.FC = () => {
  const [currentStage, setCurrentStage] = useState<GameStage>(GameStage.INTRO);
  const [currentPuzzleIndex, setCurrentPuzzleIndex] = useState<number>(0);
  const [collectedHints, setCollectedHints] = useState<string[]>([]);
  const [puzzleAttemptMessage, setPuzzleAttemptMessage] = useState<
    string | null
  >(null);
  const [finalChallengeError, setFinalChallengeError] = useState<string | null>(
    null
  );

  const [isLoading, setIsLoading] = useState<boolean>(false); // General loading for puzzles/transitions
  const [isLoadingImage, setIsLoadingImage] = useState<boolean>(false); // Specific for Imagen API call
  const [generatedImageUrl, setGeneratedImageUrl] = useState<string | null>(
    null
  );
  const [imageError, setImageError] = useState<string | null>(null);
  const [apiKeyMissingError, setApiKeyMissingError] = useState<string | null>(
    null
  );

  // Transition system state
  const [showTransition, setShowTransition] = useState<boolean>(false);
  const [transitionDoorType, setTransitionDoorType] = useState<
    "door1" | "door2" | "door3"
  >("door1");

  useEffect(() => {
    if (!process.env.API_KEY) {
      setApiKeyMissingError(
        "CRITICAL: API_KEY environment variable is not set. Image generation will fail. Please configure it and restart."
      );
      console.error("API_KEY environment variable not set.");
    }
  }, []);

  const resetGame = () => {
    setCurrentStage(GameStage.INTRO);
    setCurrentPuzzleIndex(0);
    setCollectedHints([]);
    setPuzzleAttemptMessage(null);
    setFinalChallengeError(null);
    setIsLoading(false);
    setIsLoadingImage(false);
    setGeneratedImageUrl(null);
    setImageError(null);
    setShowTransition(false);
    setTransitionDoorType("door1");
    // apiKeyMissingError is persistent unless page reloads with key
  };

  const handlePuzzleSubmit = (answer: string) => {
    const currentPuzzle = PUZZLES_DATA[currentPuzzleIndex];
    if (
      normalizeAnswer(answer) === normalizeAnswer(currentPuzzle.expectedAnswer)
    ) {
      setPuzzleAttemptMessage(`Correct! The way is open.`);
      setCollectedHints((prev) => [...prev, currentPuzzle.hintAfterSolve]);

      // Set the appropriate door type for transition
      if (currentStage === GameStage.DOOR_1_PUZZLE)
        setTransitionDoorType("door1");
      else if (currentStage === GameStage.DOOR_2_PUZZLE)
        setTransitionDoorType("door2");
      else if (currentStage === GameStage.DOOR_3_PUZZLE)
        setTransitionDoorType("door3");

      // Start the transition animation
      setShowTransition(true);
    } else {
      setPuzzleAttemptMessage("That is not the key. Try again.");
    }
  };

  const handleTransitionComplete = () => {
    setShowTransition(false);
    setPuzzleAttemptMessage(null); // Clear message for next screen

    if (currentStage === GameStage.DOOR_1_PUZZLE)
      setCurrentStage(GameStage.DOOR_1_HINT_REVEAL);
    else if (currentStage === GameStage.DOOR_2_PUZZLE)
      setCurrentStage(GameStage.DOOR_2_HINT_REVEAL);
    else if (currentStage === GameStage.DOOR_3_PUZZLE)
      setCurrentStage(GameStage.DOOR_3_HINT_REVEAL);
  };

  const handleHintContinue = () => {
    if (currentStage === GameStage.DOOR_1_HINT_REVEAL) {
      setCurrentPuzzleIndex(1);
      setCurrentStage(GameStage.DOOR_2_PUZZLE);
    } else if (currentStage === GameStage.DOOR_2_HINT_REVEAL) {
      setCurrentPuzzleIndex(2);
      setCurrentStage(GameStage.DOOR_3_PUZZLE);
    } else if (currentStage === GameStage.DOOR_3_HINT_REVEAL) {
      setCurrentStage(GameStage.CASTLE_CHALLENGE);
    }
  };

  const handleFinalAnswerSubmit = async (answer: string) => {
    if (normalizeAnswer(answer) === normalizeAnswer(FINAL_ANSWER)) {
      setFinalChallengeError(null);
      setCurrentStage(GameStage.VICTORY_REVEAL);
      if (apiKeyMissingError) {
        setImageError("Cannot generate image: API Key is missing.");
        return;
      }
      setIsLoadingImage(true);
      setImageError(null);
      try {
        const imageUrl = await generateFinalImage();
        setGeneratedImageUrl(imageUrl);
      } catch (error) {
        console.error("Error in final image generation flow:", error);
        setImageError(
          error instanceof Error
            ? error.message
            : "An unknown error occurred while generating the image."
        );
      } finally {
        setIsLoadingImage(false);
      }
    } else {
      setFinalChallengeError(
        "That is not the truth foretold. Reflect on the hints and try again."
      );
    }
  };

  const renderIntroScreen = () => (
    <div className="min-h-screen flex flex-col items-center justify-center p-4 bg-gradient-to-tr from-gray-900 via-purple-900 to-violet-600 text-center">
      <h1 className="text-6xl font-extrabold text-transparent bg-clip-text bg-gradient-to-r from-sky-400 to-purple-400 mb-6 animate-pulse">
        The Three Doors to Truth
      </h1>
      <p className="text-xl text-slate-300 mb-10 max-w-2xl">
        The Truth is a journey, not a destination. Three doors block your path.
        Behind each lies a piece of a greater puzzle. Solve them to unveil the
        truth.
      </p>
      {apiKeyMissingError && (
        <p className="text-red-400 bg-red-900 bg-opacity-70 p-4 rounded-md mb-8 max-w-xl text-sm">
          <strong>Warning:</strong> {apiKeyMissingError}
        </p>
      )}
      <button
        onClick={() => setCurrentStage(GameStage.DOOR_1_PUZZLE)}
        className="px-10 py-4 bg-gradient-to-r from-sky-500 to-indigo-600 text-white font-semibold rounded-lg shadow-xl hover:from-sky-600 hover:to-indigo-700 transform hover:scale-105 transition-all duration-300 text-lg flex items-center"
      >
        <KeyIcon className="w-6 h-6 mr-3" />
        Begin Adventure
      </button>
      <p className="mt-8 text-xs text-slate-500 max-w-md">
        Based on hints related to: The Truth is a journey, not a destination.
      </p>
    </div>
  );

  if (currentStage === GameStage.INTRO) return renderIntroScreen();

  const currentPuzzleDef = PUZZLES_DATA[currentPuzzleIndex];

  const getNextWorldTitle = () => {
    if (currentStage === GameStage.DOOR_1_PUZZLE) return "The World of Enigma";
    if (currentStage === GameStage.DOOR_2_PUZZLE)
      return "The World of Current States";
    if (currentStage === GameStage.DOOR_3_PUZZLE) return "The Castle of Truth";
    return undefined;
  };

  switch (currentStage) {
    case GameStage.DOOR_1_PUZZLE:
    case GameStage.DOOR_2_PUZZLE:
    case GameStage.DOOR_3_PUZZLE:
      return (
        <>
          <DoorPuzzleScreen
            puzzle={currentPuzzleDef}
            onSolve={handlePuzzleSubmit}
            isLoading={isLoading}
            attemptMessage={puzzleAttemptMessage}
          />
          <WorldTransition
            isActive={showTransition}
            doorType={transitionDoorType}
            onTransitionComplete={handleTransitionComplete}
            successMessage="Correct! The way is open."
            nextWorldTitle={getNextWorldTitle()}
          />
        </>
      );
    case GameStage.DOOR_1_HINT_REVEAL:
    case GameStage.DOOR_2_HINT_REVEAL:
    case GameStage.DOOR_3_HINT_REVEAL:
      return (
        <HintRevealScreen
          doorNumber={currentPuzzleDef.id}
          hintText={currentPuzzleDef.hintAfterSolve}
          onContinue={handleHintContinue}
        />
      );
    case GameStage.CASTLE_CHALLENGE:
      return (
        <CastleChallengeScreen
          collectedHints={collectedHints}
          onSubmitAnswer={handleFinalAnswerSubmit}
          isLoading={isLoadingImage}
          errorMessage={finalChallengeError}
        />
      );
    case GameStage.VICTORY_REVEAL:
      return (
        <VictoryScreen
          imageUrl={generatedImageUrl}
          onPlayAgain={resetGame}
          isLoadingImage={isLoadingImage}
          imageError={imageError || apiKeyMissingError} // Show API key error here too if it exists
        />
      );
    default:
      return <p>Error: Unknown game stage!</p>;
  }
};

export default App;
