import React from 'react';

interface SVGDoorProps {
  doorType: 'door1' | 'door2' | 'door3';
  className?: string;
}

const SVGDoor: React.FC<SVGDoorProps> = ({ doorType, className = '' }) => {
  const getDoorColors = (type: string) => {
    switch (type) {
      case 'door1':
        return {
          frame: '#8B4513',
          door: '#D2691E',
          accent: '#CD853F',
          handle: '#FFD700'
        };
      case 'door2':
        return {
          frame: '#2F4F2F',
          door: '#228B22',
          accent: '#32CD32',
          handle: '#ADFF2F'
        };
      case 'door3':
        return {
          frame: '#4B0082',
          door: '#8A2BE2',
          accent: '#9370DB',
          handle: '#DDA0DD'
        };
      default:
        return {
          frame: '#696969',
          door: '#A9A9A9',
          accent: '#C0C0C0',
          handle: '#FFD700'
        };
    }
  };

  const colors = getDoorColors(doorType);

  return (
    <svg
      width="200"
      height="300"
      viewBox="0 0 200 300"
      className={className}
      xmlns="http://www.w3.org/2000/svg"
    >
      {/* Door Frame */}
      <rect
        x="10"
        y="10"
        width="180"
        height="280"
        fill={colors.frame}
        stroke="#000"
        strokeWidth="2"
        rx="10"
      />
      
      {/* Left Door Panel */}
      <g id="leftPanel">
        <rect
          x="20"
          y="20"
          width="75"
          height="260"
          fill={colors.door}
          stroke={colors.accent}
          strokeWidth="2"
          rx="5"
        />
        
        {/* Left Door Details */}
        <rect
          x="25"
          y="30"
          width="65"
          height="50"
          fill="none"
          stroke={colors.accent}
          strokeWidth="1"
          rx="3"
        />
        <rect
          x="25"
          y="90"
          width="65"
          height="100"
          fill="none"
          stroke={colors.accent}
          strokeWidth="1"
          rx="3"
        />
        <rect
          x="25"
          y="200"
          width="65"
          height="50"
          fill="none"
          stroke={colors.accent}
          strokeWidth="1"
          rx="3"
        />
        
        {/* Left Door Handle */}
        <circle
          cx="80"
          cy="150"
          r="4"
          fill={colors.handle}
          stroke="#000"
          strokeWidth="1"
        />
      </g>
      
      {/* Right Door Panel */}
      <g id="rightPanel">
        <rect
          x="105"
          y="20"
          width="75"
          height="260"
          fill={colors.door}
          stroke={colors.accent}
          strokeWidth="2"
          rx="5"
        />
        
        {/* Right Door Details */}
        <rect
          x="110"
          y="30"
          width="65"
          height="50"
          fill="none"
          stroke={colors.accent}
          strokeWidth="1"
          rx="3"
        />
        <rect
          x="110"
          y="90"
          width="65"
          height="100"
          fill="none"
          stroke={colors.accent}
          strokeWidth="1"
          rx="3"
        />
        <rect
          x="110"
          y="200"
          width="65"
          height="50"
          fill="none"
          stroke={colors.accent}
          strokeWidth="1"
          rx="3"
        />
        
        {/* Right Door Handle */}
        <circle
          cx="120"
          cy="150"
          r="4"
          fill={colors.handle}
          stroke="#000"
          strokeWidth="1"
        />
      </g>
      
      {/* Door Hinges */}
      <rect x="15" y="40" width="8" height="12" fill="#333" rx="2" />
      <rect x="15" y="80" width="8" height="12" fill="#333" rx="2" />
      <rect x="15" y="200" width="8" height="12" fill="#333" rx="2" />
      <rect x="15" y="240" width="8" height="12" fill="#333" rx="2" />
      
      <rect x="177" y="40" width="8" height="12" fill="#333" rx="2" />
      <rect x="177" y="80" width="8" height="12" fill="#333" rx="2" />
      <rect x="177" y="200" width="8" height="12" fill="#333" rx="2" />
      <rect x="177" y="240" width="8" height="12" fill="#333" rx="2" />
      
      {/* Mystical Symbol in Center */}
      <g transform="translate(100, 150)">
        {doorType === 'door1' && (
          <g>
            {/* Mirror symbol */}
            <circle cx="0" cy="0" r="15" fill="none" stroke={colors.handle} strokeWidth="2" />
            <circle cx="0" cy="0" r="8" fill={colors.handle} opacity="0.3" />
          </g>
        )}
        {doorType === 'door2' && (
          <g>
            {/* Cipher symbol */}
            <polygon 
              points="-10,-10 10,-10 10,10 -10,10" 
              fill="none" 
              stroke={colors.handle} 
              strokeWidth="2" 
            />
            <text x="0" y="5" textAnchor="middle" fill={colors.handle} fontSize="12" fontFamily="serif">?</text>
          </g>
        )}
        {doorType === 'door3' && (
          <g>
            {/* Star symbol */}
            <polygon 
              points="0,-12 3,-3 12,-3 6,3 9,12 0,6 -9,12 -6,3 -12,-3 -3,-3" 
              fill={colors.handle} 
              opacity="0.8"
            />
          </g>
        )}
      </g>
    </svg>
  );
};

export default SVGDoor;
