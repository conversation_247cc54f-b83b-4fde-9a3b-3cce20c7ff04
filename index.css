/* Tailwind CSS directives */
@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom CSS for The Three Doors to Truth */

/* Radial gradient utility for Tailwind */
.bg-gradient-radial {
  background: radial-gradient(circle, var(--tw-gradient-stops));
}

/* Additional custom animations */
@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

.animate-shimmer {
  animation: shimmer 2s infinite linear;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  background-size: 200% 100%;
}

/* Particle effect for transitions */
@keyframes float {
  0%,
  100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

/* Glow effect for doors */
.glow {
  box-shadow: 0 0 20px rgba(59, 130, 246, 0.5);
}

.glow-amber {
  box-shadow: 0 0 20px rgba(245, 158, 11, 0.5);
}

.glow-emerald {
  box-shadow: 0 0 20px rgba(16, 185, 129, 0.5);
}

.glow-purple {
  box-shadow: 0 0 20px rgba(147, 51, 234, 0.5);
}
