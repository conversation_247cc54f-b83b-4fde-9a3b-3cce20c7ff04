import React from 'react';
import { motion } from 'framer-motion';

interface ParticleEffectProps {
  isActive: boolean;
  particleCount?: number;
  color?: string;
  className?: string;
}

const ParticleEffect: React.FC<ParticleEffectProps> = ({
  isActive,
  particleCount = 12,
  color = 'blue',
  className = ''
}) => {
  if (!isActive) return null;

  const getParticleColor = (color: string) => {
    switch (color) {
      case 'amber':
        return 'bg-amber-400';
      case 'emerald':
        return 'bg-emerald-400';
      case 'purple':
        return 'bg-purple-400';
      case 'blue':
      default:
        return 'bg-blue-400';
    }
  };

  return (
    <div className={`absolute inset-0 overflow-hidden pointer-events-none ${className}`}>
      {[...Array(particleCount)].map((_, i) => (
        <motion.div
          key={i}
          className={`absolute w-1 h-1 ${getParticleColor(color)} rounded-full opacity-80`}
          style={{
            left: `${Math.random() * 100}%`,
            top: `${Math.random() * 100}%`,
          }}
          initial={{
            scale: 0,
            opacity: 0,
          }}
          animate={{
            scale: [0, 1, 0],
            opacity: [0, 1, 0],
            x: [0, (Math.random() - 0.5) * 200],
            y: [0, (Math.random() - 0.5) * 200],
          }}
          transition={{
            duration: 2 + Math.random() * 2,
            delay: Math.random() * 1,
            ease: "easeOut",
          }}
        />
      ))}
      
      {/* Larger sparkle particles */}
      {[...Array(Math.floor(particleCount / 3))].map((_, i) => (
        <motion.div
          key={`sparkle-${i}`}
          className={`absolute w-2 h-2 ${getParticleColor(color)} rounded-full opacity-60`}
          style={{
            left: `${20 + Math.random() * 60}%`,
            top: `${20 + Math.random() * 60}%`,
          }}
          initial={{
            scale: 0,
            rotate: 0,
          }}
          animate={{
            scale: [0, 1.5, 0],
            rotate: [0, 180, 360],
            opacity: [0, 0.8, 0],
          }}
          transition={{
            duration: 3 + Math.random() * 2,
            delay: Math.random() * 2,
            ease: "easeInOut",
          }}
        />
      ))}
    </div>
  );
};

export default ParticleEffect;
