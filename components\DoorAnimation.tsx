import React from "react";
import { motion, AnimatePresence } from "framer-motion";
import SV<PERSON><PERSON><PERSON> from "./SVGDoor";

interface DoorAnimationProps {
  isOpen: boolean;
  doorType?: "door1" | "door2" | "door3";
  onAnimationComplete?: () => void;
  className?: string;
}

const DoorAnimation: React.FC<DoorAnimationProps> = ({
  isOpen,
  doorType = "door1",
  onAnimationComplete,
  className = "",
}) => {
  // Door styles based on type
  const getDoorStyle = (type: string) => {
    const baseStyle = "relative w-64 h-96 mx-auto";
    switch (type) {
      case "door1":
        return `${baseStyle} bg-gradient-to-b from-amber-800 to-amber-900`;
      case "door2":
        return `${baseStyle} bg-gradient-to-b from-emerald-800 to-emerald-900`;
      case "door3":
        return `${baseStyle} bg-gradient-to-b from-purple-800 to-purple-900`;
      default:
        return `${baseStyle} bg-gradient-to-b from-gray-800 to-gray-900`;
    }
  };

  const getDoorAccentColor = (type: string) => {
    switch (type) {
      case "door1":
        return "border-amber-600";
      case "door2":
        return "border-emerald-600";
      case "door3":
        return "border-purple-600";
      default:
        return "border-gray-600";
    }
  };

  const getGlowColor = (type: string) => {
    switch (type) {
      case "door1":
        return "shadow-amber-500/50";
      case "door2":
        return "shadow-emerald-500/50";
      case "door3":
        return "shadow-purple-500/50";
      default:
        return "shadow-gray-500/50";
    }
  };

  return (
    <div className={`flex items-center justify-center ${className}`}>
      <div className="relative">
        {/* Door Frame */}
        <div className="absolute inset-0 bg-stone-800 rounded-lg border-4 border-stone-600 shadow-2xl"></div>

        {/* Door Left Panel */}
        <motion.div
          className={`${getDoorStyle(
            doorType
          )} rounded-l-lg border-4 ${getDoorAccentColor(doorType)} shadow-xl`}
          style={{
            transformOrigin: "left center",
            zIndex: 10,
          }}
          animate={{
            rotateY: isOpen ? -120 : 0,
          }}
          transition={{
            duration: 2,
            ease: [0.25, 0.46, 0.45, 0.94],
          }}
          onAnimationComplete={onAnimationComplete}
        >
          {/* Door Details */}
          <div className="absolute inset-4 border-2 border-current opacity-30 rounded"></div>
          <div className="absolute top-1/2 right-4 w-3 h-3 bg-current rounded-full opacity-60"></div>

          {/* Door Panels */}
          <div className="absolute top-8 left-4 right-4 h-16 border border-current opacity-20 rounded"></div>
          <div className="absolute bottom-8 left-4 right-4 h-16 border border-current opacity-20 rounded"></div>
        </motion.div>

        {/* Door Right Panel */}
        <motion.div
          className={`${getDoorStyle(
            doorType
          )} rounded-r-lg border-4 ${getDoorAccentColor(
            doorType
          )} shadow-xl absolute top-0 left-32`}
          style={{
            transformOrigin: "right center",
            zIndex: 10,
          }}
          animate={{
            rotateY: isOpen ? 120 : 0,
          }}
          transition={{
            duration: 2,
            ease: [0.25, 0.46, 0.45, 0.94],
          }}
        >
          {/* Door Details */}
          <div className="absolute inset-4 border-2 border-current opacity-30 rounded"></div>
          <div className="absolute top-1/2 left-4 w-3 h-3 bg-current rounded-full opacity-60"></div>

          {/* Door Panels */}
          <div className="absolute top-8 left-4 right-4 h-16 border border-current opacity-20 rounded"></div>
          <div className="absolute bottom-8 left-4 right-4 h-16 border border-current opacity-20 rounded"></div>
        </motion.div>

        {/* Magical Light Effect */}
        <AnimatePresence>
          {isOpen && (
            <motion.div
              className={`absolute inset-0 rounded-lg ${getGlowColor(
                doorType
              )} shadow-2xl`}
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{
                opacity: [0, 1, 0.7, 1, 0.8],
                scale: [0.8, 1.1, 1, 1.05, 1],
              }}
              exit={{ opacity: 0, scale: 0.8 }}
              transition={{
                duration: 2,
                ease: "easeInOut",
              }}
            />
          )}
        </AnimatePresence>

        {/* Portal Light Behind Door */}
        <AnimatePresence>
          {isOpen && (
            <motion.div
              className="absolute inset-0 bg-gradient-radial from-white via-blue-200 to-transparent rounded-lg"
              initial={{ opacity: 0 }}
              animate={{
                opacity: [0, 0.3, 0.6, 0.4, 0.7],
              }}
              exit={{ opacity: 0 }}
              transition={{
                duration: 2,
                ease: "easeInOut",
              }}
              style={{ zIndex: 5 }}
            />
          )}
        </AnimatePresence>
      </div>
    </div>
  );
};

export default DoorAnimation;
