import React, { useRef, useEffect, useCallback } from 'react';
import { motion } from 'framer-motion';

interface WorldIntroVideoProps {
  videoSrc?: string;
  worldTitle: string;
  onVideoEnd?: () => void;
  className?: string;
}

const WorldIntroVideo: React.FC<WorldIntroVideoProps> = ({
  videoSrc,
  worldTitle,
  onVideoEnd,
  className = ''
}) => {
  const videoRef = useRef<HTMLVideoElement | null>(null);

  const handleVideoEnd = useCallback(() => {
    if (onVideoEnd) {
      onVideoEnd();
    }
  }, [onVideoEnd]);

  useEffect(() => {
    if (videoRef.current && videoSrc) {
      videoRef.current.addEventListener('ended', handleVideoEnd);
      return () => {
        if (videoRef.current) {
          videoRef.current.removeEventListener('ended', handleVideoEnd);
        }
      };
    }
  }, [videoSrc, handleVideoEnd]);

  // If no video source, show animated placeholder
  if (!videoSrc) {
    return (
      <motion.div
        className={`flex flex-col items-center justify-center ${className}`}
        initial={{ opacity: 0, scale: 0.8 }}
        animate={{ opacity: 1, scale: 1 }}
        exit={{ opacity: 0, scale: 0.8 }}
        transition={{ duration: 0.8 }}
      >
        <motion.div
          className="w-32 h-32 mb-6 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center"
          animate={{ 
            scale: [1, 1.1, 1],
            rotate: [0, 180, 360]
          }}
          transition={{ duration: 3, ease: "easeInOut" }}
        >
          <span className="text-4xl">🌟</span>
        </motion.div>
        
        <motion.h3
          className="text-3xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-blue-400 to-purple-400 mb-4 text-center"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.5, duration: 1 }}
        >
          {worldTitle}
        </motion.h3>
        
        <motion.p
          className="text-lg text-gray-300 text-center"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 1, duration: 1 }}
        >
          Prepare for your next challenge...
        </motion.p>

        {/* Particle effects */}
        <div className="absolute inset-0 overflow-hidden pointer-events-none">
          {[...Array(6)].map((_, i) => (
            <motion.div
              key={i}
              className="absolute w-2 h-2 bg-blue-400 rounded-full opacity-60"
              style={{
                left: `${20 + i * 15}%`,
                top: `${30 + (i % 2) * 40}%`,
              }}
              animate={{
                y: [-20, -40, -20],
                opacity: [0.6, 1, 0.6],
              }}
              transition={{
                duration: 2 + i * 0.5,
                repeat: Infinity,
                ease: "easeInOut",
              }}
            />
          ))}
        </div>
      </motion.div>
    );
  }

  // Render actual video if source is provided
  return (
    <motion.div
      className={`flex flex-col items-center justify-center ${className}`}
      initial={{ opacity: 0, scale: 0.8 }}
      animate={{ opacity: 1, scale: 1 }}
      exit={{ opacity: 0, scale: 0.8 }}
      transition={{ duration: 0.8 }}
    >
      <div className="relative">
        <video
          ref={videoRef}
          src={videoSrc}
          autoPlay
          muted
          className="w-full max-w-md mx-auto rounded-lg shadow-2xl"
          onEnded={handleVideoEnd}
        />
        
        {/* Video overlay with title */}
        <motion.div
          className="absolute bottom-4 left-4 right-4 bg-black bg-opacity-70 rounded-lg p-3"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.5, duration: 0.8 }}
        >
          <h3 className="text-white text-lg font-semibold text-center">
            {worldTitle}
          </h3>
        </motion.div>
      </div>
    </motion.div>
  );
};

export default WorldIntroVideo;
