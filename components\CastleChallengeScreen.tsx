
import React, { useState } from 'react';
import { CastleIcon, LightBulbIcon } from './icons';
import LoadingIndicator from './LoadingIndicator';

interface CastleChallengeScreenProps {
  collectedHints: string[];
  onSubmitAnswer: (answer: string) => void;
  isLoading: boolean;
  errorMessage: string | null;
}

const CastleChallengeScreen: React.FC<CastleChallengeScreenProps> = ({ collectedHints, onSubmitAnswer, isLoading, errorMessage }) => {
  const [answer, setAnswer] = useState('');

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSubmitAnswer(answer.trim());
  };

  return (
    <div className="min-h-screen flex flex-col items-center justify-center p-4 bg-gradient-to-br from-indigo-800 to-purple-900 text-slate-100">
      <div className="bg-slate-800 p-8 rounded-xl shadow-2xl w-full max-w-3xl transform transition-all hover:scale-105 duration-300">
        <div className="flex items-center mb-6">
          <CastleIcon className="w-16 h-16 text-amber-400 mr-4" />
          <div>
            <h1 className="text-4xl font-bold text-amber-300">The Castle of Truth</h1>
            <p className="text-slate-300">The final enigma awaits.</p>
          </div>
        </div>

        <p className="mb-6 text-slate-200 text-lg">
          You stand within the castle, the echoes of the hints fresh in your mind.
          Combine the wisdom you've gathered. What single historical and theoretical concept,
          central to Marxist-Leninist thought, do these three statements collectively describe?
        </p>

        <div className="mb-8 space-y-4">
          <h2 className="text-2xl font-semibold text-sky-300 mb-3">Collected Wisdom:</h2>
          {collectedHints.map((hint, index) => (
            <div key={index} className="bg-slate-700 p-4 rounded-lg shadow">
              <p className="flex items-start text-slate-200 italic">
                <LightBulbIcon className="w-5 h-5 text-yellow-400 mr-2 mt-1 flex-shrink-0" />
                <span>{hint}</span>
              </p>
            </div>
          ))}
        </div>

        <form onSubmit={handleSubmit} className="space-y-6">
          <div>
            <label htmlFor="final-answer" className="block text-sm font-medium text-amber-300 mb-1">
              Unveil the Truth (Enter the full Vietnamese phrase):
            </label>
            <input
              id="final-answer"
              type="text"
              value={answer}
              onChange={(e) => setAnswer(e.target.value)}
              className="w-full px-4 py-3 bg-slate-900 border border-slate-700 rounded-lg shadow-sm focus:ring-2 focus:ring-amber-500 focus:border-amber-500 placeholder-slate-500 text-slate-100"
              placeholder="Your final deduction..."
              disabled={isLoading}
            />
          </div>

          {errorMessage && (
            <p className="text-sm text-red-400 bg-red-900_bg-opacity-50 p-3 rounded-md">{errorMessage}</p>
          )}

          {isLoading ? (
            <LoadingIndicator />
          ) : (
            <button
              type="submit"
              className="w-full flex items-center justify-center px-6 py-3 border border-transparent text-base font-medium rounded-lg shadow-sm text-slate-900 bg-amber-400 hover:bg-amber-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-slate-800 focus:ring-amber-500 transition-colors duration-150"
              disabled={isLoading}
            >
              Declare the Truth
            </button>
          )}
        </form>
      </div>
    </div>
  );
};

export default CastleChallengeScreen;
