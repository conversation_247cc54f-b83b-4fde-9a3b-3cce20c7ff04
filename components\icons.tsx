
import React from 'react';

export const DoorIcon: React.FC<{ className?: string }> = ({ className }) => (
  <svg className={className} xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor">
    <path d="M18 2H6C4.89543 2 4 2.89543 4 4V20C4 21.1046 4.89543 22 6 22H18C19.1046 22 20 21.1046 20 20V4C20 2.89543 19.1046 2 18 2ZM17 11H15V13H17V11Z" />
  </svg>
);

export const KeyIcon: React.FC<{ className?: string }> = ({ className }) => (
  <svg className={className} xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor">
    <path d="M16.707 3.293C15.536 2.121 13.793 2.121 12.621 3.293L4.293 11.621C3.902 12.012 3.902 12.645 4.293 13.036C4.684 13.427 5.316 13.427 5.707 13.036L14.036 4.707C14.817 3.926 15.983 3.926 16.707 4.707C17.488 5.488 17.488 6.654 16.707 7.435L9.414 14.728L11.536 16.849L18.828 9.557C20.391 7.994 20.391 5.517 18.828 3.954L16.707 3.293ZM7 15C5.343 15 4 16.343 4 18C4 19.657 5.343 21 7 21C8.657 21 10 19.657 10 18C10 16.343 8.657 15 7 15Z" />
  </svg>
);

export const CastleIcon: React.FC<{ className?: string }> = ({ className }) => (
  <svg className={className} xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor">
    <path d="M12 2L1 9L4 10.94V22H9V16H15V22H20V10.94L23 9L12 2ZM11 10H13V13H11V10ZM6 12.5V20H7V14H17V20H18V12.5L12 8.5L6 12.5Z" />
  </svg>
);

export const LightBulbIcon: React.FC<{ className?: string }> = ({ className }) => (
  <svg className={className} xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor">
    <path d="M12 2C8.68629 2 6 4.68629 6 8C6 10.2797 7.21858 12.2458 9 13.3196V17H8C7.44772 17 7 17.4477 7 18C7 18.5523 7.44772 19 8 19H16C16.5523 19 17 18.5523 17 18C17 17.4477 16.5523 17 16 17H15V13.3196C16.7814 12.2458 18 10.2797 18 8C18 4.68629 15.3137 2 12 2ZM12 4C14.2091 4 16 5.79086 16 8C16 9.61061 15.1116 11.0028 13.8078 11.7516C13.4379 11.9616 13.2044 12.3553 13.2044 12.7956V15H10.7956V12.7956C10.7956 12.3553 10.5621 11.9616 10.1922 11.7516C8.88836 11.0028 8 9.61061 8 8C8 5.79086 9.79086 4 12 4ZM10 20H14V21C14 21.5523 13.5523 22 13 22H11C10.4477 22 10 21.5523 10 21V20Z" />
  </svg>
);

export const SunIcon: React.FC<{ className?: string }> = ({ className }) => (
  <svg className={className} xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor">
    <path d="M12 4C11.4477 4 11 4.44772 11 5V7C11 7.55228 11.4477 8 12 8C12.5523 8 13 7.55228 13 7V5C13 4.44772 12.5523 4 12 4ZM4.92893 4.92893C4.53841 4.53841 3.90524 4.53841 3.51472 4.92893C3.1242 5.31946 3.1242 5.95262 3.51472 6.34315L4.92893 7.75736C5.31946 8.14788 5.95262 8.14788 6.34315 7.75736C6.73367 7.36683 6.73367 6.73367 6.34315 6.34315L4.92893 4.92893ZM20.4853 3.51472C20.0948 3.1242 19.4616 3.1242 19.0711 3.51472L17.6569 4.92893C17.2663 5.31946 17.2663 5.95262 17.6569 6.34315C18.0474 6.73367 18.6805 6.73367 19.0711 6.34315L20.4853 4.92893C20.8758 4.53841 20.8758 3.90524 20.4853 3.51472ZM4 11C3.44772 11 3 11.4477 3 12C3 12.5523 3.44772 13 4 13H5C5.55228 13 6 12.5523 6 12C6 11.4477 5.55228 11 5 11H4ZM19 11H17C16.4477 11 16 11.4477 16 12C16 12.5523 16.4477 13 17 13H19C19.5523 13 20 12.5523 20 12C20 11.4477 19.5523 11 19 11ZM12 16C9.79086 16 8 14.2091 8 12C8 9.79086 9.79086 8 12 8C14.2091 8 16 9.79086 16 12C16 14.2091 14.2091 16 12 16ZM6.34315 17.6569C5.95262 17.2663 5.31946 17.2663 4.92893 17.6569L3.51472 19.0711C3.1242 19.4616 3.1242 20.0948 3.51472 20.4853C3.90524 20.8758 4.53841 20.8758 4.92893 20.4853L6.34315 19.0711C6.73367 18.6805 6.73367 18.0474 6.34315 17.6569ZM19.0711 17.6569C18.6805 17.2663 18.0474 17.2663 17.6569 17.6569L16.2426 19.0711C15.8521 19.4616 15.8521 20.0948 16.2426 20.4853C16.6332 20.8758 17.2663 20.8758 17.6569 20.4853L19.0711 19.0711C19.4616 18.6805 19.4616 18.0474 19.0711 17.6569ZM12 17C11.4477 17 11 17.4477 11 18V20C11 20.5523 11.4477 21 12 21C12.5523 21 13 20.5523 13 20V18C13 17.4477 12.5523 17 12 17Z" />
  </svg>
);
