import React from 'react';
import { FINAL_ANSWER } from '../constants';
import { SunIcon } from './icons';

interface VictoryScreenProps {
  onPlayAgain: () => void;
}

const VictoryScreen: React.FC<VictoryScreenProps> = ({ onPlayAgain }) => {
  const victoryImageUrl = 'https://iili.io/FTQGi3x.md.png';

  return (
    <div className="min-h-screen flex flex-col items-center justify-center p-4 bg-gradient-to-br from-yellow-400 via-orange-500 to-red-600 text-slate-900">
      <div className="bg-white bg-opacity-90 p-8 rounded-xl shadow-2xl w-full max-w-3xl text-center transform transition-all hover:scale-105 duration-300">
        <h1 className="text-5xl font-extrabold text-transparent bg-clip-text bg-gradient-to-r from-orange-500 to-red-700 mb-4">
          The Truth Is Revealed!
        </h1>
        <p className="text-slate-700 text-xl mb-6">
          The answer resonated with power, and the castle transformed!
        </p>

        <div className="my-8 p-4 border-4 border-dashed border-amber-600 rounded-lg bg-amber-50 min-h-[300px] flex items-center justify-center">
          <img
            src={victoryImageUrl}
            alt="The Revealed Truth"
            className="max-w-full max-h-[500px] h-auto rounded-lg shadow-lg object-contain"
          />
        </div>

        <p className="text-2xl font-semibold text-slate-800 mb-2">You have found it:</p>
        <p className="text-3xl font-bold text-red-700 mb-10 p-3 bg-yellow-100 rounded-md inline-block">
          {FINAL_ANSWER}
        </p>

        <button
          onClick={onPlayAgain}
          className="w-full px-8 py-4 border border-transparent text-lg font-medium rounded-lg shadow-sm text-white bg-slate-800 hover:bg-slate-900 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-amber-100 focus:ring-slate-700 transition-colors duration-150"
        >
          Embark on a New Quest
        </button>
      </div>
    </div>
  );
};

export default VictoryScreen;
