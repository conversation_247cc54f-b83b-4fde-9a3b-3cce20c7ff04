
import { GoogleGenAI } from "@google/genai";
import { IMAGEN_PROMPT } from '../constants';

const API_KEY = process.env.API_KEY;

if (!API_KEY) {
  // This will be caught by the App component and shown to the user.
  // In a real production app, you might have more sophisticated error handling or logging.
  console.error("API_KEY environment variable not set.");
}

const ai = new GoogleGenAI({ apiKey: API_KEY || "NO_KEY_FOUND" }); // Provide a fallback for type safety if key is missing

export const generateFinalImage = async (): Promise<string> => {
  if (!API_KEY) {
    throw new Error("API key is not configured. Please set the API_KEY environment variable.");
  }

  try {
    const response = await ai.models.generateImages({
        model: 'imagen-3.0-generate-002',
        prompt: IMAGEN_PROMPT,
        config: { numberOfImages: 1, outputMimeType: 'image/png' }, // Request PNG for better quality
    });

    if (response.generatedImages && response.generatedImages.length > 0) {
      const base64ImageBytes: string = response.generatedImages[0].image.imageBytes;
      return `data:image/png;base64,${base64ImageBytes}`;
    } else {
      throw new Error("No image generated or unexpected response structure.");
    }
  } catch (error) {
    console.error("Error generating image with Imagen:", error);
    if (error instanceof Error) {
        throw new Error(`Failed to generate image: ${error.message}`);
    }
    throw new Error("Failed to generate image due to an unknown error.");
  }
};
