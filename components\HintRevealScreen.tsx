
import React from 'react';
import { LightBulbIcon } from './icons';

interface HintRevealScreenProps {
  doorNumber: number;
  hintText: string;
  onContinue: () => void;
}

const HintRevealScreen: React.FC<HintRevealScreenProps> = ({ doorNumber, hintText, onContinue }) => {
  return (
    <div className="min-h-screen flex flex-col items-center justify-center p-4 bg-gradient-to-br from-slate-800 to-slate-900 text-slate-100">
      <div className="bg-slate-700 p-8 rounded-xl shadow-2xl w-full max-w-2xl text-center transform transition-all hover:scale-105 duration-300">
        <LightBulbIcon className="w-20 h-20 text-yellow-400 mx-auto mb-6" />
        <h1 className="text-3xl font-bold text-yellow-300 mb-4">Hint Unveiled!</h1>
        <p className="text-slate-300 mb-6">Behind Door {doorNumber}, you found this piece of wisdom:</p>
        
        <div className="bg-slate-800 p-6 rounded-lg mb-8 shadow-inner">
          <p className="text-slate-100 text-lg italic leading-relaxed">"{hintText}"</p>
        </div>

        <button
          onClick={onContinue}
          className="w-full px-6 py-3 border border-transparent text-base font-medium rounded-lg shadow-sm text-white bg-emerald-600 hover:bg-emerald-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-slate-800 focus:ring-emerald-500 transition-colors duration-150"
        >
          {doorNumber < 3 ? `Proceed to Door ${doorNumber + 1}` : "Enter the Castle"}
        </button>
      </div>
    </div>
  );
};

export default HintRevealScreen;
