
import { Puzzle } from './types';

export const HINTS_CONTENT: string[] = [
  "Phát biểu 1: <PERSON><PERSON> một giai đoạn mà trong đó sự xung đột giữa tàn dư của cái cũ và mầm mống của cái mới tạo nên trạng thái chưa hoàn chỉnh, nhưng không thể bị bỏ qua.",
  "Phát biểu 2: <PERSON><PERSON><PERSON> ấy, nguyên tắc công bằng chưa vượt lên lý tưởng, nhưng đủ sức ngăn con người sa vào chủ nghĩa cá nhân hoặc chủ nghĩa không tưởng.",
  "Phát biểu 3: Dù không phải là đích đến của nhân loại, giai đoạn này lại đóng vai trò tất yếu trong tiến trình phủ định biện chứng – vượt bỏ cái cũ để hình thành cái mới trên nền cao hơn."
];

export const PUZZLES_DATA: Puzzle[] = [
  {
    id: 1,
    doorTitle: "The Door of Reflection",
    question: "Một cửa ngỏ mỏng manh chói lòa ánh bạc, nối giữa hai mặt của thế giới, thật và ảo.",
    description: "Nhập vào 2 từ (tổng cộng 8 chữ cái) của vật thể được nói đến trong câu hỏi.",
    expectedAnswer: "cái gương",
    hintAfterSolve: HINTS_CONTENT[0],
  },
  {
    id: 2,
    doorTitle: "The Door of Enigma",
    question: "Vén màn bí ấn nằm trong mật thư sau đây:",
    description: "Nhập vào đáp án sau khi giải mã được mật thư.",
    expectedAnswer: "marxist philosophy",
    hintAfterSolve: HINTS_CONTENT[1],
    imagePath: "https://iili.io/Fu3MSa4.md.jpg", // Changed path to be relative
  },
  {
    id: 3,
    doorTitle: "The Door of Current States",
    question: "Hiện số lượng quốc gia vẫn còn theo chủ nghĩa xã hội là bao nhiêu?",
    description: "Nhập vào số lượng chính xác.",
    expectedAnswer: "5",
    hintAfterSolve: HINTS_CONTENT[2],
  }
];

export const FINAL_ANSWER: string = "Thời kỳ quá độ lên Chủ nghĩa xã hội";

export const IMAGEN_PROMPT: string = "A symbolic, stylized sun with a clear, golden hammer and sickle emblem at its core. The sun is large and radiant, casting warm, bright golden light. It is rising and prominently visible through a grand, arched window of an ancient stone castle chamber. The room is bathed in this illuminating light, creating a sense of hope and revelation. Digital painting, dramatic lighting, vibrant colors.";