import React, { useState, useEffect, useCallback } from "react";
import { motion, AnimatePresence } from "framer-motion";
import DoorAnimation from "./DoorAnimation";
import SoundEffect from "./SoundEffect";
import WorldIntroVideo from "./WorldIntroVideo";

interface WorldTransitionProps {
  isActive: boolean;
  doorType?: "door1" | "door2" | "door3";
  onTransitionComplete: () => void;
  successMessage?: string;
  nextWorldTitle?: string;
  introVideoSrc?: string;
}

const WorldTransition: React.FC<WorldTransitionProps> = ({
  isActive,
  doorType = "door1",
  onTransitionComplete,
  successMessage = "Correct! The way is open.",
  nextWorldTitle,
  introVideoSrc,
}) => {
  const [phase, setPhase] = useState<
    "success" | "door-opening" | "world-intro" | "complete"
  >("success");
  const [playSound, setPlaySound] = useState(false);

  // Reset state when transition becomes active
  useEffect(() => {
    if (isActive) {
      setPhase("success");
      setPlaySound(false);
    }
  }, [isActive]);

  // Handle phase transitions
  useEffect(() => {
    if (!isActive) return;

    let timer: NodeJS.Timeout;

    switch (phase) {
      case "success":
        // Show success message for 1.5 seconds, then start door opening
        timer = setTimeout(() => {
          setPhase("door-opening");
          setPlaySound(true);
        }, 1500);
        break;

      case "door-opening":
        // Door animation takes 2 seconds, then move to world intro or complete
        timer = setTimeout(() => {
          if (introVideoSrc || nextWorldTitle) {
            setPhase("world-intro");
          } else {
            setPhase("complete");
          }
        }, 2000);
        break;

      case "world-intro":
        // Show world intro for 3 seconds, then complete
        timer = setTimeout(() => {
          setPhase("complete");
        }, 3000);
        break;

      case "complete":
        // Notify parent that transition is complete
        timer = setTimeout(() => {
          onTransitionComplete();
        }, 500);
        break;
    }

    return () => {
      if (timer) clearTimeout(timer);
    };
  }, [phase, isActive, onTransitionComplete, introVideoSrc, nextWorldTitle]);

  const handleDoorAnimationComplete = useCallback(() => {
    // Door animation completed, sound should be finishing around now
    setPlaySound(false);
  }, []);

  const handleSoundEnd = useCallback(() => {
    // Sound finished playing
    console.log("Portal sound finished");
  }, []);

  const handleSoundError = useCallback((error: string) => {
    console.error("Sound error:", error);
    // Continue with transition even if sound fails
  }, []);

  if (!isActive) return null;

  return (
    <AnimatePresence>
      <motion.div
        className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-90"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        transition={{ duration: 0.5 }}
      >
        {/* Sound Effect */}
        <SoundEffect
          audioSrc="./assets/magical-portal-open.wav"
          play={playSound}
          volume={0.7}
          onEnded={handleSoundEnd}
          onError={handleSoundError}
        />

        {/* Success Message Phase */}
        {phase === "success" && (
          <motion.div
            className="text-center"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            transition={{ duration: 0.5 }}
          >
            <h2 className="text-4xl font-bold text-green-400 mb-4">
              {successMessage}
            </h2>
            <div className="flex justify-center">
              <motion.div
                className="w-16 h-16 border-4 border-green-400 border-t-transparent rounded-full"
                animate={{ rotate: 360 }}
                transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
              />
            </div>
          </motion.div>
        )}

        {/* Door Opening Phase */}
        {phase === "door-opening" && (
          <motion.div
            className="text-center"
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.8 }}
            transition={{ duration: 0.5 }}
          >
            <DoorAnimation
              isOpen={true}
              doorType={doorType}
              onAnimationComplete={handleDoorAnimationComplete}
              className="mb-8"
            />
            <motion.p
              className="text-xl text-blue-300"
              animate={{ opacity: [0.5, 1, 0.5] }}
              transition={{ duration: 2, repeat: Infinity }}
            >
              The portal opens...
            </motion.p>
          </motion.div>
        )}

        {/* World Introduction Phase */}
        {phase === "world-intro" && (
          <WorldIntroVideo
            videoSrc={introVideoSrc}
            worldTitle={nextWorldTitle || "New World"}
            className="max-w-2xl mx-auto px-4"
          />
        )}
      </motion.div>
    </AnimatePresence>
  );
};

export default WorldTransition;
